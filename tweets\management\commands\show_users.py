from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from tweets.models import Profile, Tweet, Follow

class Command(BaseCommand):
    help = 'Display all users with their information'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== ALL USERS INFORMATION ===\n'))
        
        users = User.objects.all().order_by('-date_joined')
        
        if not users:
            self.stdout.write(self.style.WARNING('No users found.'))
            return
        
        for user in users:
            # Get user profile
            try:
                profile = Profile.objects.get(user=user)
            except Profile.DoesNotExist:
                profile = None
            
            # Get user statistics
            tweet_count = Tweet.objects.filter(user=user).count()
            followers_count = Follow.objects.filter(following=user).count()
            following_count = Follow.objects.filter(follower=user).count()
            
            # Display user information
            self.stdout.write(f"{'='*50}")
            self.stdout.write(f"Username: {user.username}")
            self.stdout.write(f"Email: {user.email}")
            self.stdout.write(f"First Name: {user.first_name or 'Not set'}")
            self.stdout.write(f"Last Name: {user.last_name or 'Not set'}")
            self.stdout.write(f"Is Staff: {user.is_staff}")
            self.stdout.write(f"Is Active: {user.is_active}")
            self.stdout.write(f"Date Joined: {user.date_joined}")
            self.stdout.write(f"Last Login: {user.last_login or 'Never'}")
            self.stdout.write(f"Password Hash: {user.password[:30]}... (ENCRYPTED - Cannot be decrypted)")
            self.stdout.write(f"Tweets: {tweet_count}")
            self.stdout.write(f"Followers: {followers_count}")
            self.stdout.write(f"Following: {following_count}")
            
            if profile:
                self.stdout.write(f"Bio: {profile.bio or 'No bio'}")
                self.stdout.write(f"Profile Picture: {profile.profile_picture.name if profile.profile_picture else 'Default'}")
            
            self.stdout.write("")  # Empty line for separation
        
        self.stdout.write(self.style.SUCCESS(f'\nTotal Users: {users.count()}'))
