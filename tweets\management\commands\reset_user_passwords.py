from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.contrib.auth.hashers import make_password

class Command(BaseCommand):
    help = 'Reset all user passwords to a default password for admin access'

    def add_arguments(self, parser):
        parser.add_argument(
            '--password',
            type=str,
            default='admin123',
            help='Default password to set for all users (default: admin123)'
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm that you want to reset all passwords'
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will reset ALL user passwords!\n'
                    'Use --confirm flag to proceed.\n'
                    'Example: python manage.py reset_user_passwords --password=newpass123 --confirm'
                )
            )
            return

        default_password = options['password']
        users = User.objects.all()
        
        self.stdout.write(self.style.SUCCESS('=== RESETTING ALL USER PASSWORDS ===\n'))
        
        reset_count = 0
        for user in users:
            old_hash = user.password
            user.set_password(default_password)
            user.save()
            reset_count += 1
            
            self.stdout.write(f"✅ Reset password for: {user.username}")
            self.stdout.write(f"   Email: {user.email}")
            self.stdout.write(f"   New Password: {default_password}")
            self.stdout.write(f"   Old Hash: {old_hash[:50]}...")
            self.stdout.write(f"   New Hash: {user.password[:50]}...")
            self.stdout.write("")
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n🎉 Successfully reset passwords for {reset_count} users!\n'
                f'All users can now login with password: "{default_password}"\n'
                f'⚠️  Remember to ask users to change their passwords after login!'
            )
        )
