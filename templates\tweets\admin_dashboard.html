{% extends 'base.html' %}

{% block title %}Admin Dashboard | Twitter Clone{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">Admin Dashboard</h1>
                <div>
                    <a href="/admin/" class="btn btn-primary me-2">Django Admin</a>
                    <span class="badge bg-success">{{ total_users }} Users</span>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">{{ total_users }}</h5>
                            <p class="card-text">Total Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">{{ total_tweets }}</h5>
                            <p class="card-text">Total Tweets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">{{ total_likes }}</h5>
                            <p class="card-text">Total Likes</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">{{ total_comments }}</h5>
                            <p class="card-text">Total Comments</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger">{{ total_follows }}</h5>
                            <p class="card-text">Total Follows</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">All Users Information</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Name</th>
                                    <th>Password Hash</th>
                                    <th>Status</th>
                                    <th>Joined</th>
                                    <th>Last Login</th>
                                    <th>Stats</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for data in user_data %}
                                <tr>
                                    <td>{{ data.user.id }}</td>
                                    <td>
                                        <strong>{{ data.user.username }}</strong>
                                        {% if data.user.is_superuser %}
                                            <span class="badge bg-danger ms-1">Super</span>
                                        {% elif data.user.is_staff %}
                                            <span class="badge bg-warning ms-1">Staff</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ data.user.email|default:"Not set" }}</td>
                                    <td>{{ data.user.first_name }} {{ data.user.last_name|default:"" }}</td>
                                    <td>
                                        <code class="small">{{ data.password_hash }}</code>
                                    </td>
                                    <td>
                                        {% if data.user.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ data.user.date_joined|date:"M d, Y" }}</td>
                                    <td>
                                        {% if data.user.last_login %}
                                            {{ data.user.last_login|date:"M d, Y H:i" }}
                                        {% else %}
                                            <span class="text-muted">Never</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>
                                            <div>Tweets: {{ data.tweet_count }}</div>
                                            <div>Followers: {{ data.followers_count }}</div>
                                            <div>Following: {{ data.following_count }}</div>
                                            <div>Likes: {{ data.likes_count }}</div>
                                            <div>Comments: {{ data.comments_count }}</div>
                                        </small>
                                    </td>
                                    <td>
                                        <a href="{% url 'tweets:profile' data.user.username %}" class="btn btn-sm btn-outline-primary">View Profile</a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center">No users found.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="alert alert-warning mt-4">
                <h6><i class="fas fa-exclamation-triangle"></i> Security Notice</h6>
                <p class="mb-0">
                    <strong>Password Hashes:</strong> The passwords shown above are cryptographically hashed and cannot be reversed to reveal the original passwords. 
                    This is a security feature. If a user forgets their password, you should use Django's password reset functionality or manually set a new password through the Django admin.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
