{% extends 'base.html' %}

{% block title %}Reset Password | Twitter Clone{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">Reset Password</h3>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-key text-primary" style="font-size: 3rem;"></i>
                    <h4 class="mt-3">Forgot your password?</h4>
                    <p class="text-muted">Enter your username and create a new password.</p>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required 
                               placeholder="Enter your username">
                        <div class="form-text">
                            Enter the username of your account.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required 
                               placeholder="Enter new password">
                        <div class="form-text">
                            Password must be at least 6 characters long.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required 
                               placeholder="Confirm new password">
                        <div class="form-text">
                            Re-enter your new password to confirm.
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary rounded-pill">
                            <i class="fas fa-save me-1"></i> Reset Password
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Remember your password? <a href="{% url 'tweets:login' %}" class="text-decoration-none fw-bold">Login</a></p>
            </div>
        </div>
    </div>
</div>

<script>
// Add some client-side validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    form.addEventListener('submit', function(e) {
        if (newPassword.value !== confirmPassword.value) {
            e.preventDefault();
            alert('Passwords do not match!');
            confirmPassword.focus();
        } else if (newPassword.value.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long!');
            newPassword.focus();
        }
    });
    
    // Real-time password match checking
    confirmPassword.addEventListener('input', function() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}
