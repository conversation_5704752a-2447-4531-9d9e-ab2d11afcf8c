{% extends 'base.html' %}

{% block title %}Register | Twitter Clone{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">Register</h3>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% for field in form %}
                        <div class="mb-3">
                            <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                            {{ field }}
                            {% if field.help_text %}
                                <small class="form-text text-muted">{{ field.help_text }}</small>
                            {% endif %}
                            {% for error in field.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Register</button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Already have an account? <a href="{% url 'tweets:login' %}">Login</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Style for Django form fields */
    .form-control {
        display: block;
        width: 100%;
    }
    
    /* Override Django's default styling for form fields */
    input, select, textarea {
        display: block;
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    /* Style for help text */
    .form-text {
        margin-top: 0.25rem;
        font-size: 0.875em;
    }
</style>
{% endblock %}
