{% extends 'base.html' %}
{% load static %}

{% block title %}Home | Twitter Clone{% endblock %}

{% block content %}
<div class="row">
    <!-- Left Sidebar - User Info -->
    <div class="col-md-3">
        <div class="card mb-4">
            {% if user.is_authenticated %}
                <div class="card-body p-0">
                    <div class="p-3 bg-primary text-white rounded-top">
                        <div class="text-center position-relative">
                            <img src="{{ user.profile.profile_picture.url }}" alt="{{ user.username }}" class="rounded-circle img-fluid border border-3 border-white" style="width: 120px; height: 120px; object-fit: cover; margin-bottom: -40px;">
                        </div>
                    </div>
                    <div class="p-4 pt-5 text-center">
                        <h4 class="mt-2 fw-bold">{{ user.username }}</h4>
                        <p class="text-muted">@{{ user.username }}</p>

                        <div class="d-flex justify-content-around mb-3">
                            <div class="text-center">
                                <div class="fw-bold">{{ user.profile.get_tweets_count }}</div>
                                <small class="text-muted">Tweets</small>
                            </div>
                            <div class="text-center">
                                <div class="fw-bold">{{ user.profile.get_following_count }}</div>
                                <small class="text-muted">Following</small>
                            </div>
                            <div class="text-center">
                                <div class="fw-bold">{{ user.profile.get_followers_count }}</div>
                                <small class="text-muted">Followers</small>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="{% url 'tweets:profile' user.username %}" class="btn btn-outline-primary rounded-pill">
                                <i class="fas fa-user me-1"></i> View Profile
                            </a>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="card-body">
                    <div class="text-center">
                        <i class="fab fa-twitter text-primary mb-3" style="font-size: 3rem;"></i>
                        <h4 class="fw-bold">Welcome to Twitter Clone</h4>
                        <p class="text-muted mb-4">Join the conversation today!</p>
                        <div class="d-grid gap-2">
                            <a href="{% url 'tweets:login' %}" class="btn btn-primary rounded-pill mb-2">
                                <i class="fas fa-sign-in-alt me-1"></i> Login
                            </a>
                            <a href="{% url 'tweets:register' %}" class="btn btn-outline-primary rounded-pill">
                                <i class="fas fa-user-plus me-1"></i> Register
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Main Content - Tweet Form and Timeline -->
    <div class="col-md-6">
        {% if user.is_authenticated %}
            <!-- Tweet Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="d-flex align-items-start mb-3">
                            <img src="{{ user.profile.profile_picture.url }}" alt="{{ user.username }}" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                            <div class="flex-grow-1">
                                <div class="mb-3">
                                    {{ form.content }}
                                    <div class="d-flex justify-content-between mt-2">
                                        <small class="text-muted"><span id="char-count">0</span>/280</small>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="image-upload-container">
                                        <label for="{{ form.image.id_for_label }}" class="btn btn-sm btn-outline-primary rounded-pill">
                                            <i class="far fa-image me-1"></i> Add Image
                                        </label>
                                        <span class="selected-file-name ms-2 text-muted" id="selected-file-name"></span>
                                        {{ form.image }}
                                    </div>
                                    <button type="submit" class="btn btn-primary rounded-pill px-4">
                                        <i class="fas fa-paper-plane me-1"></i> Tweet
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        {% endif %}

        <!-- Tweets Timeline -->
        <div class="tweets">
            <h4 class="mb-3">{% if user.is_authenticated %}Your Timeline{% else %}Recent Tweets{% endif %}</h4>

            {% if tweets %}
                {% for tweet in tweets %}
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <a href="{% url 'tweets:profile' tweet.user.username %}" class="text-decoration-none">
                                    <img src="{{ tweet.user.profile.profile_picture.url }}" alt="{{ tweet.user.username }}" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                </a>
                                <div>
                                    <a href="{% url 'tweets:profile' tweet.user.username %}" class="text-decoration-none">
                                        <h5 class="mb-0 fw-bold">{{ tweet.user.username }}</h5>
                                    </a>
                                    <small class="text-muted">@{{ tweet.user.username }} · {{ tweet.created_at|timesince }} ago</small>
                                </div>
                            </div>
                            <p class="tweet-content">{{ tweet.content }}</p>
                            {% if tweet.image %}
                                <div class="mb-3">
                                    <img src="{{ tweet.image.url }}" alt="Tweet image" class="img-fluid rounded mb-2 w-100" style="object-fit: cover; max-height: 350px;">
                                </div>
                            {% endif %}
                            <div class="d-flex justify-content-between mt-3">
                                <a href="{% url 'tweets:tweet_detail' tweet.id %}" class="text-decoration-none btn btn-sm btn-outline-secondary rounded-pill">
                                    <i class="far fa-comment"></i> {{ tweet.get_comment_count }}
                                </a>
                                <a href="{% url 'tweets:like_tweet' tweet.id %}" class="text-decoration-none like-btn btn btn-sm {% if user.is_authenticated and user in tweet.likes.all %}btn-danger{% else %}btn-outline-danger{% endif %} rounded-pill" data-tweet-id="{{ tweet.id }}">
                                    {% if user.is_authenticated and user in tweet.likes.all %}
                                        <i class="fas fa-heart"></i>
                                    {% else %}
                                        <i class="far fa-heart"></i>
                                    {% endif %}
                                    <span class="like-count">{{ tweet.get_like_count }}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="alert alert-info">
                    {% if user.is_authenticated %}
                        No tweets in your timeline yet. Try following more users!
                    {% else %}
                        No tweets available. Be the first to tweet!
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Right Sidebar - Who to Follow -->
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Who to Follow</h5>
            </div>
            <div class="card-body user-suggestions">
                {% if user.is_authenticated and user_suggestions %}
                    <div class="list-group">
                        {% for suggested_user in user_suggestions %}
                            <div class="list-group-item border-0 px-0 py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <img src="{{ suggested_user.profile.profile_picture.url }}" alt="{{ suggested_user.username }}" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        <div>
                                            <a href="{% url 'tweets:profile' suggested_user.username %}" class="text-decoration-none">
                                                <h6 class="mb-0 fw-bold">{{ suggested_user.username }}</h6>
                                            </a>
                                            <small class="text-muted">@{{ suggested_user.username }}</small>
                                        </div>
                                    </div>
                                    <a href="{% url 'tweets:follow_user' suggested_user.username %}" class="btn btn-sm btn-primary rounded-pill"><i class="fas fa-plus me-1"></i>Follow</a>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% elif user.is_authenticated %}
                    <div class="text-center py-3">
                        <i class="fas fa-users text-muted mb-3" style="font-size: 2rem;"></i>
                        <p class="text-muted">No suggestions available right now.</p>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-lock text-muted mb-3" style="font-size: 2rem;"></i>
                        <p class="text-muted">Log in to see user suggestions.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card trending-card">
            <div class="card-header trending-header">
                <h5 class="mb-0 text-white"><i class="fas fa-fire me-2"></i>Trending</h5>
            </div>
            <div class="card-body trending">
                {% if trending_topics %}
                    <div class="list-group">
                        {% for topic in trending_topics %}
                            <a href="{% url 'tweets:search' %}?q={{ topic.word }}" class="list-group-item list-group-item-action trending-item border-0 px-3 py-3 mb-2 rounded">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0 fw-bold trending-topic">#{{ topic.word }}</h6>
                                        <small class="trending-count">{{ topic.count }} tweets</small>
                                    </div>
                                    <i class="fas fa-chevron-right trending-icon"></i>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-chart-line trending-empty-icon mb-3" style="font-size: 2rem;"></i>
                        <p class="trending-empty-text">No trending topics available right now.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // AJAX for liking tweets
        $('.like-btn').click(function(e) {
            e.preventDefault();

            // Check if user is authenticated
            {% if not user.is_authenticated %}
                window.location.href = "{% url 'tweets:login' %}";
                return;
            {% endif %}

            var tweetId = $(this).data('tweet-id');
            var likeUrl = "{% url 'tweets:like_tweet' 0 %}".replace('0', tweetId);
            var likeBtn = $(this);

            // Disable the button temporarily to prevent multiple clicks
            likeBtn.addClass('disabled');

            $.ajax({
                url: likeUrl,
                type: 'GET',
                headers: {'X-Requested-With': 'XMLHttpRequest'},
                success: function(data) {
                    // Find all instances of this tweet's like button (might be on multiple pages)
                    var allLikeBtns = $('[data-tweet-id="' + tweetId + '"]');

                    allLikeBtns.each(function() {
                        var btn = $(this);
                        var icon = btn.find('i');
                        var count = btn.find('.like-count');

                        if (data.liked) {
                            icon.removeClass('far fa-heart').addClass('fas fa-heart');
                            btn.removeClass('btn-outline-danger').addClass('btn-danger');
                        } else {
                            icon.removeClass('fas fa-heart').addClass('far fa-heart');
                            btn.removeClass('btn-danger').addClass('btn-outline-danger');
                        }

                        count.text(data.like_count);
                    });
                },
                complete: function() {
                    // Re-enable the button after the request completes
                    setTimeout(function() {
                        likeBtn.removeClass('disabled');
                    }, 500);
                }
            });
        });

        // Character counter for tweet content
        $('#id_content').on('input', function() {
            var charCount = $(this).val().length;
            $('#char-count').text(charCount);

            if (charCount > 280) {
                $('#char-count').addClass('text-danger fw-bold');
            } else {
                $('#char-count').removeClass('text-danger fw-bold');
            }
        });

        // Display selected image filename
        $('#id_image').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            if (fileName) {
                $('#selected-file-name').text(fileName);
            } else {
                $('#selected-file-name').text('');
            }
        });

        // Hide the file input and style the label
        $('#id_image').hide();
    });
</script>
{% endblock %}
