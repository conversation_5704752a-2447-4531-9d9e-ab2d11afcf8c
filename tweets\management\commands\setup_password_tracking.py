from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from tweets.models import UserPasswordTracker

class Command(BaseCommand):
    help = 'Set up password tracking for existing users with known passwords'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== SETTING UP PASSWORD TRACKING ===\n'))
        
        # Known passwords for existing users (you can modify these)
        known_passwords = {
            'dinesh': 'admin123',
            'sangita': 'admin123', 
            'user': 'admin123',
            'ritik': 'password123',  # Common default passwords
            'priyank': 'password123',
            'crick': 'password123',
        }
        
        users = User.objects.all()
        
        for user in users:
            # Try to get existing tracker or create new one
            tracker, created = UserPasswordTracker.objects.get_or_create(
                user=user,
                defaults={'plain_password': known_passwords.get(user.username, 'unknown')}
            )
            
            if created:
                self.stdout.write(f"✅ Created password tracker for: {user.username}")
            else:
                self.stdout.write(f"📝 Updated password tracker for: {user.username}")
            
            # If we have a known password, update it
            if user.username in known_passwords:
                tracker.plain_password = known_passwords[user.username]
                tracker.save()
                self.stdout.write(f"   Tracked Password: {tracker.plain_password}")
            else:
                self.stdout.write(f"   Tracked Password: {tracker.plain_password}")
            
            self.stdout.write("")
        
        self.stdout.write(
            self.style.SUCCESS(
                f'🎉 Password tracking set up for {users.count()} users!\n'
                '⚠️  WARNING: Passwords are now stored in plain text in the database.\n'
                '⚠️  This is NOT secure and should only be used for development!'
            )
        )
