from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from tweets.models import Profile

class Command(BaseCommand):
    help = 'Display all users with their complete password hash information'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== ALL USERS WITH COMPLETE PASSWORD INFORMATION ===\n'))
        
        users = User.objects.all().order_by('-date_joined')
        
        if not users:
            self.stdout.write(self.style.WARNING('No users found.'))
            return
        
        for i, user in enumerate(users, 1):
            self.stdout.write(f"{'='*80}")
            self.stdout.write(f"USER #{i}")
            self.stdout.write(f"{'='*80}")
            self.stdout.write(f"Username: {user.username}")
            self.stdout.write(f"Email: {user.email}")
            self.stdout.write(f"First Name: {user.first_name or 'Not set'}")
            self.stdout.write(f"Last Name: {user.last_name or 'Not set'}")
            self.stdout.write(f"Is Staff: {user.is_staff}")
            self.stdout.write(f"Is Superuser: {user.is_superuser}")
            self.stdout.write(f"Is Active: {user.is_active}")
            self.stdout.write(f"Date Joined: {user.date_joined}")
            self.stdout.write(f"Last Login: {user.last_login or 'Never'}")
            
            # Show complete password hash
            self.stdout.write(f"\n--- PASSWORD INFORMATION ---")
            password_hash = user.password
            if password_hash:
                # Parse the password hash
                parts = password_hash.split('$')
                if len(parts) >= 4:
                    algorithm = parts[0]
                    iterations = parts[1]
                    salt = parts[2]
                    hash_value = parts[3]
                    
                    self.stdout.write(f"Algorithm: {algorithm}")
                    self.stdout.write(f"Iterations: {iterations}")
                    self.stdout.write(f"Salt: {salt}")
                    self.stdout.write(f"Hash: {hash_value}")
                    self.stdout.write(f"Complete Hash: {password_hash}")
                else:
                    self.stdout.write(f"Password Hash: {password_hash}")
            else:
                self.stdout.write("No password set")
            
            self.stdout.write(f"\n--- SECURITY NOTE ---")
            self.stdout.write("The password hash above is cryptographically secure.")
            self.stdout.write("It CANNOT be reversed to reveal the original password.")
            self.stdout.write("This is a security feature to protect user data.")
            
            self.stdout.write("")  # Empty line for separation
        
        self.stdout.write(self.style.SUCCESS(f'\nTotal Users: {users.count()}'))
        self.stdout.write(self.style.WARNING('\n⚠️  IMPORTANT SECURITY NOTICE:'))
        self.stdout.write('The password hashes shown above are one-way encrypted.')
        self.stdout.write('Original passwords cannot be recovered from these hashes.')
        self.stdout.write('This is a security feature that protects your users.')
        self.stdout.write('If you need to reset a user\'s password, use Django admin or create a new password.')
