{% extends 'base.html' %}
{% load static %}

{% block title %}{{ profile_user.username }} | Twitter Clone{% endblock %}

{% block content %}
<div class="row">
    <!-- Profile Information -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <img src="{{ profile_user.profile.profile_picture.url }}" alt="{{ profile_user.username }}" class="rounded-circle img-fluid mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    </div>
                    <div class="col-md-9">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>{{ profile_user.username }}</h3>
                            {% if user.is_authenticated and user != profile_user %}
                                {% if is_following %}
                                    <a href="{% url 'tweets:unfollow_user' profile_user.username %}" class="btn btn-outline-primary">Unfollow</a>
                                {% else %}
                                    <a href="{% url 'tweets:follow_user' profile_user.username %}" class="btn btn-primary">Follow</a>
                                {% endif %}
                            {% endif %}
                        </div>
                        <p class="text-muted">@{{ profile_user.username }}</p>
                        <p>{{ profile_user.profile.bio }}</p>
                        <div class="d-flex">
                            <div class="me-4">
                                <strong>{{ followers_count }}</strong> Followers
                            </div>
                            <div>
                                <strong>{{ following_count }}</strong> Following
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">Joined {{ profile_user.profile.date_joined|date:"F Y" }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Profile Edit Form (if viewing own profile) -->
    {% if user.is_authenticated and user == profile_user %}
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Edit Profile</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ u_form.username.id_for_label }}" class="form-label">Username</label>
                                {{ u_form.username }}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ u_form.email.id_for_label }}" class="form-label">Email</label>
                                {{ u_form.email }}
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="{{ p_form.bio.id_for_label }}" class="form-label">Bio</label>
                                {{ p_form.bio }}
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="{{ p_form.profile_picture.id_for_label }}" class="form-label">Profile Picture</label>
                                {{ p_form.profile_picture }}
                            </div>
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">Update Profile</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    {% endif %}
    
    <!-- User's Tweets -->
    <div class="col-md-12">
        <h4 class="mb-3">Tweets</h4>
        
        {% if tweets %}
            {% for tweet in tweets %}
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-2">
                            <img src="{{ tweet.user.profile.profile_picture.url }}" alt="{{ tweet.user.username }}" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                            <div>
                                <h6 class="mb-0">{{ tweet.user.username }}</h6>
                                <small class="text-muted">@{{ tweet.user.username }} · {{ tweet.created_at|timesince }} ago</small>
                            </div>
                        </div>
                        <p>{{ tweet.content }}</p>
                        {% if tweet.image %}
                            <img src="{{ tweet.image.url }}" alt="Tweet image" class="img-fluid rounded mb-2">
                        {% endif %}
                        <div class="d-flex justify-content-between mt-3">
                            <a href="{% url 'tweets:tweet_detail' tweet.id %}" class="text-decoration-none">
                                <i class="far fa-comment text-muted"></i> {{ tweet.get_comment_count }}
                            </a>
                            <a href="{% url 'tweets:like_tweet' tweet.id %}" class="text-decoration-none like-btn" data-tweet-id="{{ tweet.id }}">
                                {% if user.is_authenticated and user in tweet.likes.all %}
                                    <i class="fas fa-heart text-danger"></i>
                                {% else %}
                                    <i class="far fa-heart text-muted"></i>
                                {% endif %}
                                <span class="like-count">{{ tweet.get_like_count }}</span>
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="alert alert-info">
                No tweets yet.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Style for Django form fields */
    .form-control {
        display: block;
        width: 100%;
    }
    
    /* Override Django's default styling for form fields */
    input, select, textarea {
        display: block;
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // AJAX for liking tweets
        $('.like-btn').click(function(e) {
            e.preventDefault();
            var tweetId = $(this).data('tweet-id');
            var likeUrl = "{% url 'tweets:like_tweet' 0 %}".replace('0', tweetId);
            
            $.ajax({
                url: likeUrl,
                type: 'GET',
                headers: {'X-Requested-With': 'XMLHttpRequest'},
                success: function(data) {
                    var likeBtn = $('[data-tweet-id="' + tweetId + '"]');
                    var likeIcon = likeBtn.find('i');
                    var likeCount = likeBtn.find('.like-count');
                    
                    if (data.liked) {
                        likeIcon.removeClass('far fa-heart text-muted').addClass('fas fa-heart text-danger');
                    } else {
                        likeIcon.removeClass('fas fa-heart text-danger').addClass('far fa-heart text-muted');
                    }
                    
                    likeCount.text(data.like_count);
                }
            });
        });
    });
</script>
{% endblock %}
