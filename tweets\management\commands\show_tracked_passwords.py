from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from tweets.models import UserPasswordTracker

class Command(BaseCommand):
    help = 'Display all users with their tracked plain text passwords'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== ALL USERS WITH TRACKED PASSWORDS ===\n'))
        
        users = User.objects.all().order_by('-date_joined')
        
        if not users:
            self.stdout.write(self.style.WARNING('No users found.'))
            return
        
        for i, user in enumerate(users, 1):
            self.stdout.write(f"{'='*60}")
            self.stdout.write(f"USER #{i}: {user.username}")
            self.stdout.write(f"{'='*60}")
            self.stdout.write(f"Username: {user.username}")
            self.stdout.write(f"Email: {user.email}")
            self.stdout.write(f"First Name: {user.first_name or 'Not set'}")
            self.stdout.write(f"Last Name: {user.last_name or 'Not set'}")
            self.stdout.write(f"Is Staff: {user.is_staff}")
            self.stdout.write(f"Is Active: {user.is_active}")
            self.stdout.write(f"Date Joined: {user.date_joined}")
            self.stdout.write(f"Last Login: {user.last_login or 'Never'}")
            
            # Get tracked password
            try:
                tracker = UserPasswordTracker.objects.get(user=user)
                tracked_password = tracker.plain_password or 'Not tracked'
                self.stdout.write(f"\n🔑 TRACKED PASSWORD: {tracked_password}")
                self.stdout.write(f"📅 Password tracked since: {tracker.created_at}")
                self.stdout.write(f"🔄 Last updated: {tracker.updated_at}")
            except UserPasswordTracker.DoesNotExist:
                self.stdout.write(f"\n❌ No password tracking for this user")
            
            # Show hash for comparison
            self.stdout.write(f"\n🔒 Password Hash: {user.password[:50]}...")
            self.stdout.write("")
        
        self.stdout.write(self.style.SUCCESS(f'\nTotal Users: {users.count()}'))
        self.stdout.write(self.style.WARNING('\n⚠️  SECURITY WARNING:'))
        self.stdout.write('The passwords shown above are stored in plain text.')
        self.stdout.write('This is NOT secure and should only be used for development!')
        self.stdout.write('In production, never store passwords in plain text!')
