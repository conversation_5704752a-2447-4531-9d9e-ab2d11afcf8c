{% extends 'base.html' %}

{% block title %}Set New Password | Twitter Clone{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">Set New Password</h3>
            </div>
            <div class="card-body">
                {% if validlink %}
                    <div class="text-center mb-4">
                        <i class="fas fa-lock text-primary" style="font-size: 3rem;"></i>
                        <h4 class="mt-3">Create New Password</h4>
                        <p class="text-muted">Enter your new password below.</p>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label">New Password</label>
                            {{ form.new_password1 }}
                            {% if form.new_password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.new_password1.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                <ul class="mb-0">
                                    <li>Your password can't be too similar to your other personal information.</li>
                                    <li>Your password must contain at least 8 characters.</li>
                                    <li>Your password can't be a commonly used password.</li>
                                    <li>Your password can't be entirely numeric.</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label">Confirm New Password</label>
                            {{ form.new_password2 }}
                            {% if form.new_password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.new_password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary rounded-pill">
                                <i class="fas fa-save me-1"></i> Set New Password
                            </button>
                        </div>
                    </form>
                {% else %}
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                        <h4 class="mt-3 text-warning">Invalid Reset Link</h4>
                        <div class="alert alert-warning">
                            <p class="mb-2">This password reset link is invalid or has expired.</p>
                            <p class="mb-0">Please request a new password reset.</p>
                        </div>
                        <div class="mt-4">
                            <a href="{% url 'tweets:password_reset' %}" class="btn btn-primary">
                                <i class="fas fa-redo me-1"></i> Request New Reset Link
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
