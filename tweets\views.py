from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib.auth import login, authenticate, logout
from django.contrib import messages
from django.http import JsonResponse, HttpResponseRedirect
from django.urls import reverse
from django.db.models import Q, Count
from collections import Counter
import re

from .models import Profile, Tweet, Comment, Like, Follow
from .forms import UserRegisterForm, UserUpdateForm, ProfileUpdateForm, TweetForm, CommentForm

def get_trending_topics():
    # Get all tweets from the last 7 days
    recent_tweets = Tweet.objects.all().order_by('-created_at')[:100]

    # Extract words from tweets
    words = []
    for tweet in recent_tweets:
        # Remove special characters and split by spaces
        tweet_words = re.sub(r'[^\w\s]', '', tweet.content.lower()).split()
        # Filter out common words (you can expand this list)
        stop_words = ['the', 'a', 'an', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'to', 'of', 'in', 'for', 'with', 'on', 'at', 'by', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my', 'your', 'his', 'her', 'its', 'our', 'their']
        tweet_words = [word for word in tweet_words if word not in stop_words and len(word) > 2]
        words.extend(tweet_words)

    # Count word occurrences
    word_counts = Counter(words)

    # Get the top 5 trending words
    trending = word_counts.most_common(5)

    # Format as list of dictionaries with word and count
    return [{'word': word, 'count': count} for word, count in trending]

def home(request):
    # Show all tweets to everyone
    tweets = Tweet.objects.all()

    # Get user suggestions (users to follow)
    user_suggestions = []
    if request.user.is_authenticated:
        # Get users that the current user already follows
        following_users = Follow.objects.filter(follower=request.user).values_list('following', flat=True)
        # Get users that the current user doesn't follow yet (exclude self and already following)
        user_suggestions = User.objects.exclude(id=request.user.id).exclude(id__in=following_users)[:5]

    # Get trending topics (most used words in recent tweets)
    trending_topics = get_trending_topics()

    form = TweetForm()

    if request.method == 'POST':
        form = TweetForm(request.POST, request.FILES)
        if form.is_valid() and request.user.is_authenticated:
            tweet = form.save(commit=False)
            tweet.user = request.user
            tweet.save()
            messages.success(request, 'Your tweet has been posted!')
            return redirect('tweets:home')

    context = {
        'tweets': tweets,
        'form': form,
        'user_suggestions': user_suggestions,
        'trending_topics': trending_topics,
    }
    return render(request, 'tweets/home.html', context)

def register(request):
    if request.user.is_authenticated:
        return redirect('tweets:home')

    if request.method == 'POST':
        form = UserRegisterForm(request.POST)
        if form.is_valid():
            user = form.save()
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}! You can now log in.')
            return redirect('tweets:login')
    else:
        form = UserRegisterForm()

    return render(request, 'tweets/register.html', {'form': form})

def login_view(request):
    if request.user.is_authenticated:
        return redirect('tweets:home')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)
            messages.success(request, f'Welcome back, {username}!')
            return redirect('tweets:home')
        else:
            messages.error(request, 'Invalid username or password.')

    return render(request, 'tweets/login.html')

@login_required
def logout_view(request):
    logout(request)
    messages.success(request, 'You have been logged out.')
    return redirect('tweets:login')

def profile(request, username):
    user = get_object_or_404(User, username=username)
    tweets = Tweet.objects.filter(user=user)

    # Check if the current user follows this user
    is_following = False
    if request.user.is_authenticated:
        is_following = Follow.objects.filter(follower=request.user, following=user).exists()

    # Get follower and following counts
    followers_count = Follow.objects.filter(following=user).count()
    following_count = Follow.objects.filter(follower=user).count()

    context = {
        'profile_user': user,
        'tweets': tweets,
        'is_following': is_following,
        'followers_count': followers_count,
        'following_count': following_count,
    }

    # If this is the current user's profile, add forms for updating profile
    if request.user.is_authenticated and request.user == user:
        if request.method == 'POST':
            u_form = UserUpdateForm(request.POST, instance=request.user)
            p_form = ProfileUpdateForm(request.POST, request.FILES, instance=request.user.profile)

            if u_form.is_valid() and p_form.is_valid():
                u_form.save()
                p_form.save()
                messages.success(request, 'Your profile has been updated!')
                return redirect('tweets:profile', username=request.user.username)
        else:
            u_form = UserUpdateForm(instance=request.user)
            p_form = ProfileUpdateForm(instance=request.user.profile)

        context.update({
            'u_form': u_form,
            'p_form': p_form,
        })

    return render(request, 'tweets/profile.html', context)

def tweet_detail(request, tweet_id):
    tweet = get_object_or_404(Tweet, id=tweet_id)
    comments = Comment.objects.filter(tweet=tweet)

    # Check if the current user has liked this tweet
    is_liked = False
    if request.user.is_authenticated:
        is_liked = Like.objects.filter(tweet=tweet, user=request.user).exists()

    # Comment form
    if request.method == 'POST' and request.user.is_authenticated:
        form = CommentForm(request.POST)
        if form.is_valid():
            comment = form.save(commit=False)
            comment.tweet = tweet
            comment.user = request.user
            comment.save()
            messages.success(request, 'Your comment has been added!')
            return redirect('tweets:tweet_detail', tweet_id=tweet.id)
    else:
        form = CommentForm()

    context = {
        'tweet': tweet,
        'comments': comments,
        'is_liked': is_liked,
        'form': form,
    }
    return render(request, 'tweets/tweet_detail.html', context)

@login_required
def create_tweet(request):
    if request.method == 'POST':
        form = TweetForm(request.POST, request.FILES)
        if form.is_valid():
            tweet = form.save(commit=False)
            tweet.user = request.user
            tweet.save()
            messages.success(request, 'Your tweet has been posted!')
            return redirect('tweets:home')
    else:
        form = TweetForm()

    return render(request, 'tweets/create_tweet.html', {'form': form})

@login_required
def like_tweet(request, tweet_id):
    tweet = get_object_or_404(Tweet, id=tweet_id)

    # Check if the user already liked this tweet
    already_liked = Like.objects.filter(tweet=tweet, user=request.user).exists()

    if already_liked:
        # User already liked this tweet, so unlike it
        Like.objects.filter(tweet=tweet, user=request.user).delete()
        liked = False
    else:
        # User hasn't liked this tweet yet, so like it
        Like.objects.create(tweet=tweet, user=request.user)
        liked = True

    # If this is an AJAX request, return JSON response
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'liked': liked,
            'like_count': tweet.get_like_count(),
        })

    # Get the referring page
    referer = request.META.get('HTTP_REFERER')

    # If coming from tweet detail page, redirect back there
    if referer and f'/tweet/{tweet_id}/' in referer:
        return redirect('tweets:tweet_detail', tweet_id=tweet.id)

    # Otherwise, redirect to home
    return redirect('tweets:home')

@login_required
def add_comment(request, tweet_id):
    tweet = get_object_or_404(Tweet, id=tweet_id)

    if request.method == 'POST':
        form = CommentForm(request.POST)
        if form.is_valid():
            comment = form.save(commit=False)
            comment.tweet = tweet
            comment.user = request.user
            comment.save()
            messages.success(request, 'Your comment has been added!')

    return redirect('tweets:tweet_detail', tweet_id=tweet.id)

@login_required
def follow_user(request, username):
    user_to_follow = get_object_or_404(User, username=username)

    # Don't allow users to follow themselves
    if request.user == user_to_follow:
        messages.error(request, 'You cannot follow yourself.')
        return redirect('tweets:profile', username=username)

    # Create the follow relationship if it doesn't exist
    follow, created = Follow.objects.get_or_create(follower=request.user, following=user_to_follow)

    if created:
        messages.success(request, f'You are now following {username}!')
    else:
        messages.info(request, f'You are already following {username}.')

    return redirect('tweets:profile', username=username)

@login_required
def unfollow_user(request, username):
    user_to_unfollow = get_object_or_404(User, username=username)

    # Try to find the follow relationship
    try:
        follow = Follow.objects.get(follower=request.user, following=user_to_unfollow)
        follow.delete()
        messages.success(request, f'You have unfollowed {username}.')
    except Follow.DoesNotExist:
        messages.info(request, f'You are not following {username}.')

    return redirect('tweets:profile', username=username)

def search(request):
    query = request.GET.get('q', '')
    users = []
    tweets = []

    if query:
        # Search for users by username or email
        users = User.objects.filter(Q(username__icontains=query) | Q(email__icontains=query))

        # Search for tweets by content
        tweets = Tweet.objects.filter(content__icontains=query)

    context = {
        'query': query,
        'users': users,
        'tweets': tweets,
    }

    return render(request, 'tweets/search_results.html', context)
